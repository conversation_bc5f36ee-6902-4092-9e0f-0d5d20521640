import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import userImage from "../../../../../assets/images/tutor2.png";
import { Button } from "@/components/button/button";
import { useSelector, useDispatch } from "react-redux";
import Loader from "@/components/loader/loader";
import { useGetUpdateStudentProfileMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { toast } from "react-toastify";
import usePost from "@/hooks/usePost";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import { CustomSelect } from "@/components/select/select";
import { handleProfileUpdateResponse } from "@/utils/profileUtils";
import { convertToBase64 } from "@/utils";

// Move timezones outside component to prevent recreation on every render
const TIMEZONES = [
	{ value: "Africa/Lagos", label: "Lagos (UTC+1)" },
	{ value: "America/New_York", label: "New York (UTC-5/-4)" },
	{ value: "America/Los_Angeles", label: "Los Angeles (UTC-8/-7)" },
	{ value: "Europe/London", label: "London (UTC+0/+1)" },
	{ value: "Asia/Kolkata", label: "Kolkata (UTC+5:30)" },
	{ value: "Asia/Tokyo", label: "Tokyo (UTC+9)" },
	{ value: "Australia/Sydney", label: "Sydney (UTC+10/+11)" },
	{ value: "Europe/Paris", label: "Paris (UTC+1/+2)" },
	{ value: "Europe/Berlin", label: "Berlin (UTC+1/+2)" },
	{ value: "Africa/Cairo", label: "Cairo (UTC+2)" },
	{ value: "America/Chicago", label: "Chicago (UTC-6/-5)" },
	{ value: "America/Denver", label: "Denver (UTC-7/-6)" },
	{ value: "America/Sao_Paulo", label: "São Paulo (UTC-3)" },
	{ value: "Asia/Dubai", label: "Dubai (UTC+4)" },
	{ value: "Asia/Shanghai", label: "Shanghai (UTC+8)" },
	{ value: "Asia/Singapore", label: "Singapore (UTC+8)" },
	{ value: "Europe/Moscow", label: "Moscow (UTC+3)" },
	{ value: "Africa/Johannesburg", label: "Johannesburg (UTC+2)" },
	{ value: "Pacific/Auckland", label: "Auckland (UTC+12/+13)" },
	{ value: "America/Toronto", label: "Toronto (UTC-5/-4)" },
];

const SettingsProfile = () => {
  const user = useSelector((state) => state?.app?.userInfo?.user);
  const dispatch = useDispatch();
  const [previewImage, setPreviewImage] = useState(null);
  const { handlePost, isLoading: isUpdating } = usePost(
    useGetUpdateStudentProfileMutation
  );

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm();

	// Initialize form with user data from Redux store
	useEffect(() => {
		if (user) {
			const currentTimezone = TIMEZONES.find(
				(tz) => tz.value === user.timezone
			);
			reset({
				firstname: user?.firstname || "",
				lastname: user?.lastname || "",
				email: user?.email || "",
				phone: user?.phone || "",
				timezone: currentTimezone || "",
			});
			// Set preview image from user's current image
			setPreviewImage(user?.image);
		}
	}, [user, reset]); // Removed timezones from dependency array

  const onSubmit = async (formData) => {
    try {
      const updateData = {
        ...user, // All current values
        ...formData,
        timezone:
          formData.timezone?.value || formData.timezone || user?.timezone,
        userId: user?.id,
      };

      // Add image if it was updated
      if (previewImage) {
        updateData.image = previewImage;
      }

      // Optional: remove fields that shouldn't be sent
      delete updateData.password; // unless you're explicitly updating it
      delete updateData.__v; // if your DB attaches this

      console.log("Payload being sent:", updateData); // Log the payload to console

      const response = await handlePost(updateData);

      // Update Redux store with new user data including image
      handleProfileUpdateResponse(
        dispatch,
        user,
        response,
        () => toast.success("Profile updated successfully"),
        () => toast.error("Failed to update profile in store")
      );
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="md:max-w-[528px] w-auto">
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex text-[18px] flex-col w-auto"
      >
        {/* Profile Image Upload Section */}
        <div className="flex gap-4">
          <div className="border rounded-md w-[145px] h-[145px] overflow-hidden">
            <img
              src={previewImage || userImage}
              alt="User"
              className="object-cover w-full h-full"
            />
          </div>
          <div className="flex flex-col items-center">
            <div className="flex flex-col">
              <label className="px-6 py-2 border rounded-md cursor-pointer">
                Upload Photo
                <input
                  type="file"
                  hidden
                  aria-label="upload-photo"
                  accept="image/png, image/jpeg"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const base64 = await convertToBase64(file);
                      setPreviewImage(base64);
                    }
                  }}
                />
              </label>
            </div>
            <p className="text-[#4B5563] text-sm">
              Maximum size – 2MB <br />
              JPG or PNG format
            </p>
          </div>
        </div>

        <div className="text-[#1A1A40] mt-6 space-y-4">
          {/* First Name */}
          <div>
            <label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
              First Name
            </label>
            <input
              type="text"
              {...register("firstname", { required: "First name is required" })}
              className="border rounded-md w-full px-4 py-2"
            />
            {errors.firstname && (
              <p className="text-red-500 text-sm mt-1">
                {errors.firstname.message}
              </p>
            )}
          </div>

          {/* Last Name */}
          <div>
            <label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
              Last Name
            </label>
            <input
              type="text"
              {...register("lastname", { required: "Last name is required" })}
              className="border rounded-md w-full px-4 py-2"
            />
            {errors.lastname && (
              <p className="text-red-500 text-sm mt-1">
                {errors.lastname.message}
              </p>
            )}
          </div>

          {/* Email */}
          <div>
            <label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
              Email
            </label>
            <input
              type="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
              className="border rounded-md w-full px-4 py-2"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">
                {errors.email.message}
              </p>
            )}
          </div>

					{/* Phone Number */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Phone Number
						</label>
						<PhoneInputWithCountry
							control={control}
							name="phone"
							isRequired={true}
							error={errors.phone?.message}
							rules={{
								validate: (value) => {
									if (!value) return "Phone number is required";
									const phoneRegex = /^\+?[1-9]\d{1,14}$/; // E.164 format
									return phoneRegex.test(value) || "Invalid phone number";
								},
							}}
						/>
					</div>

					{/* Time Zone */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Time Zone
						</label>
						<CustomSelect
							name="timezone"
							control={control}
							options={TIMEZONES}
							defaultValue={
								user?.timezone
									? TIMEZONES.find((tz) => tz.value === user.timezone)
									: null
							}
							rules={{ required: "Time zone is required" }}
							placeholder="Select Time Zone"
							className="w-full"
						/>

            {errors.timezone && (
              <p className="text-red-500 text-sm mt-1">
                {errors.timezone.message}
              </p>
            )}
          </div>
        </div>

        <div className="mt-6">
          <Button
            className="w-full h-[50px] mb-3"
            type="submit"
            disabled={isUpdating}
          >
            {isUpdating ? <Loader size={24} /> : "Save Changes"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SettingsProfile;
